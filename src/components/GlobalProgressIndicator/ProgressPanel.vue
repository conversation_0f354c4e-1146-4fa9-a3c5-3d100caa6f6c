<template>
  <div class="p-4 mb-2 w-96 max-w-sm rounded-lg border shadow-lg bg-background border-border">
    <!-- 头部信息 -->
    <div class="flex justify-between items-center mb-3">
      <div class="flex gap-2 items-center">
        <div class="relative">
          <component
            :is="hasActiveTasks || hasBatchTasks ? (uploadTasks.length > 0 || batchTasks.length > 0 ? UploadIcon : DownloadIcon) : AlertCircleIcon"
            :class="cn(
              'h-2 w-2',
              hasActiveTasks || hasBatchTasks ? 'text-primary' : 'text-red-500',
              (hasActiveTasks || hasBatchTasks) && 'animate-pulse'
            )" />
        </div>
        <span class="text-sm font-medium whitespace-nowrap">
          {{ hasActiveTasks || hasBatchTasks ? `${totalActiveCount} 个任务进行中` : errorTasks.length > 0 ?
            `${errorTasks.length} 个任务失败` :
            '任务管理器' }}
        </span>
      </div>
      <Button variant="ghost" size="sm" @click="$emit('toggleMinimized')">
        <component :is="minimized ? ChevronUpIcon : ChevronDownIcon" class="w-4 h-4" />
      </Button>
    </div>

    <!-- 标签页导航 -->
    <div v-if="!minimized" class="flex mb-3 border-b">
      <button v-for="tab in tabs" :key="tab.key" @click="activeTab = tab.key" :class="cn(
        'flex-1 py-2 px-3 text-sm font-medium border-b-2 transition-colors',
        activeTab === tab.key
          ? 'border-primary text-primary'
          : 'border-transparent text-muted-foreground hover:text-foreground'
      )">
        {{ tab.label }}
        <span v-if="tab.count > 0" class="ml-1 text-xs">({{ tab.count }})</span>
      </button>
    </div>

    <!-- 总体进度条 -->
    <div v-if="hasActiveTasks || hasBatchTasks" class="mb-3">
      <div class="flex justify-between items-center mb-1 text-xs text-muted-foreground">
        <span>总体进度</span>
        <span>{{ combinedProgress }}%</span>
      </div>
      <div class="w-full h-2 rounded-full bg-secondary">
        <div class="h-2 rounded-full transition-all duration-300 bg-primary"
          :style="{ width: `${combinedProgress}%` }" />
      </div>
    </div>

    <!-- 任务列表 -->
    <div v-if="!minimized" class="max-h-64">
      <!-- 当前任务标签页 -->
      <template v-if="activeTab === 'current'">
        <!-- 虚拟列表容器 -->
        <VirtualTaskList v-if="allCurrentTasks.length > 0" :items="allCurrentTasks" :height="256" :estimate-size="65"
          :overscan="3" :get-item-key="(item) => `${item.type}-${item.id}`" ref="currentTasksVirtualList">
          <template #default="{ item }">
            <!-- 上传批量任务 -->
            <BaseBatchTaskItem v-if="item.type === 'upload-batch'" :batch-task="item.data"
              :sub-tasks="getUploadSubTasks(item.data.id)" task-type="upload" :loading="false"
              @cancel="() => handleBatchCancel(item.data.id)" @retry="() => handleBatchRetry(item.data.id)"
              @pause="() => handleBatchPause(item.data.id)" @resume="() => handleBatchResume(item.data.id)"
              @toggle-expanded="() => handleUploadBatchToggleExpanded(item.data.id)" />

            <!-- 下载批量任务 -->
            <BaseBatchTaskItem v-else-if="item.type === 'download-batch'" :batch-task="item.data"
              :sub-tasks="getDownloadSubTasks(item.data.id)" task-type="download" :loading="false"
              @cancel="() => handleDownloadBatchCancel(item.data.id)"
              @retry="() => handleDownloadBatchRetry(item.data.id)"
              @pause="() => handleDownloadBatchPause(item.data.id)"
              @resume="() => handleDownloadBatchResume(item.data.id)"
              @toggle-expanded="() => handleDownloadBatchToggleExpanded(item.data.id)" />

            <!-- 普通任务 -->
            <ProgressTaskItem v-else-if="item.type === 'task'" :task="item.data" @cancel="$emit('cancelTask', $event)"
              @remove="$emit('removeTask', $event)" @retry="$emit('retryTask', $event)"
              @pause="$emit('pauseTask', $event)" @resume="$emit('resumeTask', $event)" />
          </template>
        </VirtualTaskList>

        <!-- 当前任务空状态 -->
        <div v-else class="py-8 text-center text-muted-foreground">
          <div class="text-sm">暂无任务</div>
          <div class="mt-1 text-xs">点击上传或下载按钮开始使用</div>
        </div>
      </template>

      <!-- 历史记录标签页 -->
      <template v-else>
        <!-- 虚拟列表容器 -->
        <VirtualTaskList v-if="currentHistoryTasks.length > 0" :items="currentHistoryTasks" :height="256"
          :estimate-size="70" :overscan="3" :get-item-key="(item) => `history-${item.id}`"
          ref="historyTasksVirtualList">
          <template #default="{ item }">
            <!-- 批量任务历史 -->
            <BatchHistoryTaskItem v-if="'batchName' in item" :batch-task="item as BatchHistoryTask"
              @retry="$emit('retryHistoryTask', $event)" @remove="$emit('removeHistoryItem', $event)" />

            <!-- 普通任务历史 -->
            <HistoryTaskItem v-else :task="item as HistoryTask" @retry="$emit('retryHistoryTask', $event)"
              @remove="$emit('removeHistoryItem', $event)" />
          </template>
        </VirtualTaskList>

        <!-- 历史记录空状态 -->
        <div v-else class="py-8 text-center text-muted-foreground">
          <div class="text-sm">暂无{{ activeTab === 'upload' ? '上传' : '下载' }}历史</div>
          <div class="mt-1 text-xs">完成任务后会在这里显示</div>
        </div>
      </template>
    </div>

    <!-- 操作按钮 -->
    <div v-if="!minimized" class="pt-3 mt-3 border-t">
      <!-- 当前任务操作 -->
      <div v-if="activeTab === 'current' && (totalActiveCount > 0 || errorTasks.length > 0)" class="flex gap-2">
        <Button v-if="errorTasks.length > 0" variant="outline" size="sm" @click="$emit('clearErrorTasks')"
          class="flex-1">
          清除错误
        </Button>
        <Button variant="outline" size="sm" @click="handleClearAllTasks" class="flex-1">
          清除全部
        </Button>
      </div>

      <!-- 历史记录操作 -->
      <div v-else-if="activeTab !== 'current' && currentHistoryTasks.length > 0" class="flex gap-2">
        <Button v-if="activeTab === 'upload'" variant="outline" size="sm" @click="handleClearUploadHistory"
          :disabled="clearingHistory" class="flex-1">
          <Loader2Icon v-if="clearingHistory" class="mr-1 w-3 h-3 animate-spin" />
          清除上传历史
        </Button>
        <Button v-if="activeTab === 'download'" variant="outline" size="sm" @click="handleClearDownloadHistory"
          :disabled="clearingHistory" class="flex-1">
          <Loader2Icon v-if="clearingHistory" class="mr-1 w-3 h-3 animate-spin" />
          清除下载历史
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onUnmounted } from 'vue'
import {
  Upload as UploadIcon,
  Download as DownloadIcon,
  AlertCircle as AlertCircleIcon,
  ChevronUp as ChevronUpIcon,
  ChevronDown as ChevronDownIcon,
  Loader2 as Loader2Icon
} from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import ProgressTaskItem from './ProgressTaskItem.vue'
import HistoryTaskItem from './HistoryTaskItem.vue'
import BaseBatchTaskItem from './BaseBatchTaskItem.vue'
import BatchHistoryTaskItem from './BatchHistoryTaskItem.vue'
import VirtualTaskList from './VirtualTaskList.vue'
import { useTusUpload } from '@/components/Upload/composables/useTusUpload'
import { useStreamDownloadManager } from '@/composables/useStreamDownloadManager'
import type { ProgressTask, HistoryTask, BatchHistoryTask } from '@/composables/useGlobalProgress'

// 虚拟列表项类型定义
interface VirtualTaskItem {
  id: string
  type: 'task' | 'upload-batch' | 'download-batch'
  data: ProgressTask | any
}

interface Props {
  activeTasks: ProgressTask[]
  uploadTasks: ProgressTask[]
  errorTasks: ProgressTask[]
  hasActiveTasks: boolean
  overallProgress: number
  minimized: boolean
  uploadHistory: (HistoryTask | BatchHistoryTask)[]
  downloadHistory: (HistoryTask | BatchHistoryTask)[]
  allHistory: (HistoryTask | BatchHistoryTask)[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  toggleMinimized: []
  cancelTask: [taskId: string]
  removeTask: [taskId: string]
  retryTask: [taskId: string]
  clearErrorTasks: []
  clearAllTasks: []
  clearUploadHistory: []
  clearDownloadHistory: []
  clearHistory: []
  removeHistoryItem: [historyId: string]
  retryHistoryTask: [historyId: string]
  pauseTask: [taskId: string]
  resumeTask: [taskId: string]
}>()

// 集成 TUS 上传功能
const tusUpload = useTusUpload()
const downloadManager = useStreamDownloadManager()

// 当前活跃标签页
const activeTab = ref<'current' | 'upload' | 'download'>('current')

// Loading 状态
const clearingHistory = ref(false)

// 已处理的批量任务ID集合，避免重复清除
const processedBatchTasks = ref<Set<string>>(new Set())
const processedDownloadBatchTasks = ref<Set<string>>(new Set())

// 用于跟踪定时器，确保组件卸载时能够清理
const cleanupTimers = ref<Set<NodeJS.Timeout>>(new Set())

// 获取批量任务
const batchTasks = computed(() => tusUpload.batchTasks.value)
const activeBatchTasks = computed(() => Array.from(batchTasks.value.values()).filter(task =>
  ['pending', 'uploading'].includes(task.status)
))

// 获取下载批量任务
const downloadBatchTasks = computed(() => downloadManager.batchTasks.value)
const activeDownloadBatchTasks = computed(() => downloadBatchTasks.value.filter(task =>
  ['pending', 'downloading', 'paused'].includes(task.status)
))

const hasBatchTasks = computed(() => activeBatchTasks.value.length > 0 || activeDownloadBatchTasks.value.length > 0)

// 计算总活跃任务数（包括批量任务）
const totalActiveCount = computed(() => {
  return props.activeTasks.length + activeBatchTasks.value.length + activeDownloadBatchTasks.value.length
})

// 计算组合进度
const combinedProgress = computed(() => {
  const regularTasks = props.activeTasks
  const batchTasksActive = activeBatchTasks.value
  const downloadBatchTasksActive = activeDownloadBatchTasks.value

  const allActiveTasks = [...regularTasks, ...batchTasksActive, ...downloadBatchTasksActive]
  if (allActiveTasks.length === 0) return 0

  const totalProgress = allActiveTasks.reduce((sum, task) => sum + task.progress, 0)
  return Math.round(totalProgress / allActiveTasks.length)
})

// 标签页配置
const tabs = computed(() => [
  {
    key: 'current' as const,
    label: '当前任务',
    count: totalActiveCount.value + props.errorTasks.length
  },
  {
    key: 'upload' as const,
    label: '上传历史',
    count: props.uploadHistory.length
  },
  {
    key: 'download' as const,
    label: '下载历史',
    count: props.downloadHistory.length
  }
])

// 合并活跃任务和错误任务（批量任务已不在 tasks 中，无需过滤）
const allTasks = computed(() => {
  return [...props.activeTasks, ...props.errorTasks]
})

// 虚拟列表引用
const currentTasksVirtualList = ref()
const historyTasksVirtualList = ref()

// 为虚拟列表准备的当前任务数据
const allCurrentTasks = computed<VirtualTaskItem[]>(() => {
  const result: VirtualTaskItem[] = []

  // 添加上传批量任务
  activeBatchTasks.value.forEach(batchTask => {
    result.push({
      id: batchTask.id,
      type: 'upload-batch',
      data: batchTask
    })
  })

  // 添加下载批量任务
  activeDownloadBatchTasks.value.forEach(batchTask => {
    result.push({
      id: batchTask.id,
      type: 'download-batch',
      data: batchTask
    })
  })

  // 添加普通任务
  allTasks.value.forEach(task => {
    result.push({
      id: task.id,
      type: 'task',
      data: task
    })
  })

  return result
})

// 当前显示的历史任务 - 虚拟列表可以处理更多数据
const currentHistoryTasks = computed(() => {
  if (activeTab.value === 'upload') {
    return props.uploadHistory
  } else if (activeTab.value === 'download') {
    return props.downloadHistory
  }
  return []
})

// 监听批量任务状态变化，自动清除已完成的任务
watch(batchTasks, (newBatchTasks) => {
  const completedTasks = Array.from(newBatchTasks.values()).filter(task =>
    ['completed', 'error', 'cancelled'].includes(task.status) &&
    !processedBatchTasks.value.has(task.id) // 避免重复处理
  )

  // 延迟清除已完成的批量任务，给用户一些时间看到完成状态
  completedTasks.forEach(task => {
    // 标记为已处理
    processedBatchTasks.value.add(task.id)

    const timer = setTimeout(() => {
      console.log(`自动清除已完成的批量任务: ${task.batchName} (状态: ${task.status})`)
      tusUpload.deleteBatchTask(task.id)

      // 清除完成后从已处理集合中移除
      processedBatchTasks.value.delete(task.id)

      // 从定时器集合中移除
      cleanupTimers.value.delete(timer)
    }, 2000) // 2秒后自动清除

    // 将定时器添加到集合中以便清理
    cleanupTimers.value.add(timer)
  })
}, { deep: true })

// 监听下载批量任务状态变化，自动清除已完成的任务
watch(downloadBatchTasks, (newBatchTasks) => {
  const completedTasks = newBatchTasks.filter(task =>
    ['completed', 'error', 'cancelled'].includes(task.status) &&
    !processedDownloadBatchTasks.value.has(task.id) // 避免重复处理
  )

  // 延迟清除已完成的下载批量任务，给用户一些时间看到完成状态
  completedTasks.forEach(task => {
    // 标记为已处理
    processedDownloadBatchTasks.value.add(task.id)

    const timer = setTimeout(() => {
      console.log(`自动清除已完成的下载批量任务: ${task.batchName} (状态: ${task.status})`)
      downloadManager.removeBatchTask(task.id)

      // 清除完成后从已处理集合中移除
      processedDownloadBatchTasks.value.delete(task.id)

      // 从定时器集合中移除
      cleanupTimers.value.delete(timer)
    }, 2000) // 2秒后自动清除

    // 将定时器添加到集合中以便清理
    cleanupTimers.value.add(timer)
  })
}, { deep: true })

// 处理批量任务操作
const handleBatchCancel = async (batchId: string) => {
  try {
    await tusUpload.deleteBatchTask(batchId)
  } catch (error) {
    console.error('取消批量任务失败:', error)
  }
}

const handleBatchRetry = async (batchId: string) => {
  try {
    await tusUpload.retryBatchUpload(batchId)
  } catch (error) {
    console.error('重试批量任务失败:', error)
  }
}

const handleBatchPause = async (batchId: string) => {
  try {
    await tusUpload.pauseBatchUpload(batchId)
  } catch (error) {
    console.error('暂停批量任务失败:', error)
  }
}

const handleBatchResume = async (batchId: string) => {
  try {
    await tusUpload.resumeBatchUpload(batchId)
  } catch (error) {
    console.error('恢复批量任务失败:', error)
  }
}

// 处理下载批量任务操作
const handleDownloadBatchCancel = async (batchId: string) => {
  try {
    await downloadManager.cancelBatchDownload(batchId)
    downloadManager.removeBatchTask(batchId)
  } catch (error) {
    console.error('取消下载批量任务失败:', error)
  }
}

const handleDownloadBatchRetry = async (batchId: string) => {
  try {
    // 这里可以添加批量重试逻辑
    console.log('重试下载批量任务:', batchId)
  } catch (error) {
    console.error('重试下载批量任务失败:', error)
  }
}

const handleDownloadBatchPause = async (batchId: string) => {
  try {
    await downloadManager.pauseBatchDownload(batchId)
  } catch (error) {
    console.error('暂停下载批量任务失败:', error)
  }
}

const handleDownloadBatchResume = async (batchId: string) => {
  try {
    await downloadManager.resumeBatchDownload(batchId)
  } catch (error) {
    console.error('恢复下载批量任务失败:', error)
  }
}

// 获取上传子任务
const getUploadSubTasks = (batchId: string) => {
  return tusUpload.getBatchSubTasks(batchId)
}

// 获取下载子任务
const getDownloadSubTasks = (batchId: string) => {
  const batchTask = downloadManager.batchTasksMap.value.get(batchId)
  if (!batchTask) return []

  return batchTask.subTasks.map(taskId => {
    return downloadManager.tasksMap.value.get(taskId)
  }).filter((task): task is NonNullable<typeof task> => Boolean(task))
}

// 处理上传批量任务展开/折叠
const handleUploadBatchToggleExpanded = (batchId: string) => {
  tusUpload.toggleBatchExpanded(batchId)
}

// 处理下载批量任务展开/折叠
const handleDownloadBatchToggleExpanded = (batchId: string) => {
  const batchTask = downloadManager.batchTasksMap.value.get(batchId)
  if (batchTask) {
    const updatedBatch = { ...batchTask, expanded: !batchTask.expanded }
    downloadManager.batchTasksMap.value.set(batchId, updatedBatch)
  }
}

// 处理清除所有任务
const handleClearAllTasks = () => {
  // 清除普通任务
  emit('clearAllTasks')

  // 清除所有已完成的上传批量任务
  const completedBatchTasks = Array.from(batchTasks.value.values()).filter(task =>
    ['completed', 'error', 'cancelled'].includes(task.status)
  )

  completedBatchTasks.forEach(task => {
    tusUpload.deleteBatchTask(task.id)
  })

  // 清除所有已完成的下载批量任务
  const completedDownloadBatchTasks = downloadBatchTasks.value.filter(task =>
    ['completed', 'error', 'cancelled'].includes(task.status)
  )

  completedDownloadBatchTasks.forEach(task => {
    downloadManager.removeBatchTask(task.id)
  })
}

// 处理清除上传历史记录
const handleClearUploadHistory = async () => {
  if (clearingHistory.value) return

  clearingHistory.value = true
  try {
    await new Promise<void>((resolve) => {
      emit('clearUploadHistory')
      // 给一个短暂的延迟来显示loading状态
      setTimeout(resolve, 300)
    })
  } finally {
    clearingHistory.value = false
  }
}

// 处理清除下载历史记录
const handleClearDownloadHistory = async () => {
  if (clearingHistory.value) return

  clearingHistory.value = true
  try {
    await new Promise<void>((resolve) => {
      emit('clearDownloadHistory')
      // 给一个短暂的延迟来显示loading状态
      setTimeout(resolve, 300)
    })
  } finally {
    clearingHistory.value = false
  }
}

// 组件卸载时清理所有定时器
onUnmounted(() => {
  cleanupTimers.value.forEach(timer => {
    clearTimeout(timer)
  })
  cleanupTimers.value.clear()
})
</script>