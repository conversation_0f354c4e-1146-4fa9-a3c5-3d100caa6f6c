<template>
  <div class="batch-progress-task">
    <!-- 批量任务头部 -->
    <div class="batch-task-header" @click="$emit('toggle-expanded')">
      <div class="batch-task-icon">
        <component :is="taskIcon" class="w-4 h-4" :class="taskIconClass" />
      </div>

      <div class="batch-task-info">
        <div class="batch-task-title">
          <span class="batch-name">{{ batchTask.batchName }}</span>
          <span class="batch-stats">
            ({{ batchTask.completedFiles }}/{{ batchTask.totalFiles }} 完成)
          </span>
        </div>

        <div class="batch-task-meta">
          <span class="batch-size">{{ formatFileSize(batchTask.totalSize) }}</span>
          <span class="batch-status" :class="statusClass">{{ statusText }}</span>
        </div>

        <!-- 进度条 -->
        <div class="progress-container">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: `${batchTask.progress}%` }" :class="progressClass"></div>
          </div>
          <span class="progress-text">{{ batchTask.progress }}%</span>
        </div>
      </div>

      <div class="batch-task-actions">
        <!-- 展开/折叠按钮 -->
        <Button variant="ghost" size="sm" class="expand-button">
          <ChevronDown class="w-4 h-4 transition-transform" :class="{ 'rotate-180': batchTask.expanded }" />
        </Button>

        <!-- 批量操作按钮 -->
        <div class="action-buttons">
          <Button v-if="batchTask.status === 'error'" variant="ghost" size="sm" @click.stop="$emit('retry')"
            :disabled="loading">
            <RotateCcw class="w-4 h-4" />
          </Button>

          <!-- 暂停按钮 (上传和下载都支持) -->
          <Button v-if="isTaskInProgress" variant="ghost" size="sm" @click.stop="$emit('pause')" :disabled="loading">
            <Pause class="w-4 h-4" />
          </Button>

          <!-- 恢复按钮 (上传和下载都支持) -->
          <Button v-if="batchTask.status === 'paused'" variant="ghost" size="sm" @click.stop="$emit('resume')"
            :disabled="loading">
            <Play class="w-4 h-4" />
          </Button>

          <Button variant="ghost" size="sm" @click.stop="$emit('cancel')" :disabled="loading"
            class="text-destructive hover:text-destructive">
            <X class="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>

    <!-- 子任务列表 -->
    <div v-if="batchTask.expanded" class="sub-tasks-container">
      <div class="sub-tasks-header">
        <span class="sub-tasks-title">文件详情</span>
        <span class="sub-tasks-count">{{ subTasks.length }} 个文件</span>
      </div>

      <div class="sub-tasks-list">
        <div v-for="subTask in subTasks" :key="subTask.id" class="sub-task-item">
          <div class="sub-task-icon">
            <FileIcon class="w-3 h-3 text-muted-foreground" />
          </div>

          <div class="sub-task-info">
            <div class="sub-task-name">{{ subTask.fileName }}</div>
            <div class="sub-task-meta">
              <span class="file-size">{{ formatFileSize(subTask.fileSize || 0) }}</span>
              <span class="task-status" :class="getSubTaskStatusClass(subTask.status)">
                {{ getSubTaskStatusText(subTask.status) }}
              </span>
            </div>
          </div>

          <div class="sub-task-progress">
            <div v-if="isSubTaskInProgress(subTask.status)" class="progress-info">
              <span class="progress-percent">{{ subTask.progress || 0 }}%</span>
              <div class="mini-progress-bar">
                <div class="mini-progress-fill" :style="{ width: `${subTask.progress || 0}%` }"></div>
              </div>
            </div>
            <div v-else class="status-indicator" :class="getSubTaskStatusClass(subTask.status)">
              <Check v-if="subTask.status === 'completed'" class="w-3 h-3" />
              <X v-else-if="subTask.status === 'error'" class="w-3 h-3" />
              <Pause v-else-if="subTask.status === 'paused'" class="w-3 h-3" />
              <Clock v-else class="w-3 h-3" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  FolderOpen, FolderDown, ChevronDown, RotateCcw, X, Pause, Play,
  FileIcon, Check, Clock
} from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { getStatusClass, getStatusText, getProgressClass, formatFileSize } from '@/lib/upload-utils'

// 通用批量任务接口
interface BaseBatchTask {
  id: string
  type: 'batch'
  batchName: string
  folderPath?: string
  totalFiles: number
  completedFiles: number
  failedFiles: number
  pausedFiles?: number
  totalSize: number
  progress: number
  status: string
  startTime: Date
  endTime?: Date
  error?: string
  expanded?: boolean
}

// 通用子任务接口
interface BaseSubTask {
  id: string
  fileName: string
  fileSize?: number
  progress?: number
  status: string
  error?: string
}

// Props
interface Props {
  batchTask: BaseBatchTask
  subTasks: BaseSubTask[]
  taskType: 'upload' | 'download'
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// Emits
defineEmits<{
  'cancel': []
  'retry': []
  'pause': []
  'resume': []
  'toggle-expanded': []
}>()

// 计算任务图标
const taskIcon = computed(() => {
  return props.taskType === 'upload' ? FolderOpen : FolderDown
})

// 计算任务图标样式
const taskIconClass = computed(() => {
  return props.taskType === 'upload' ? 'text-blue-500' : 'text-green-500'
})

// 判断任务是否正在进行中
const isTaskInProgress = computed(() => {
  const progressStatuses = props.taskType === 'upload'
    ? ['uploading', 'pending']
    : ['downloading', 'pending']
  return progressStatuses.includes(props.batchTask.status)
})

// 状态计算 - 使用工具函数
const statusClass = computed(() => getStatusClass(props.batchTask.status as any))
const statusText = computed(() => getStatusText(props.batchTask.status as any))
const progressClass = computed(() => getProgressClass(props.batchTask.status as any))

// 子任务状态工具函数
const getSubTaskStatusClass = (status: string) => getStatusClass(status as any)
const getSubTaskStatusText = (status: string) => getStatusText(status as any)

// 判断子任务是否在进行中
const isSubTaskInProgress = (status: string) => {
  return status === 'uploading' || status === 'downloading'
}
</script>

<style scoped>
.batch-progress-task {
  background-color: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  overflow: hidden;
}

.batch-task-header {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  cursor: pointer;
  gap: 0.5rem;
  transition: background-color 0.2s ease;
}

.batch-task-header:hover {
  background-color: hsl(var(--accent));
}

.batch-task-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 0.25rem;
  background-color: hsl(var(--primary) / 0.1);
}

.batch-task-info {
  flex: 1;
  min-width: 0;
}

.batch-task-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.125rem;
}

.batch-name {
  font-weight: 500;
  font-size: 0.75rem;
  color: hsl(var(--foreground));
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.batch-stats {
  font-size: 0.7rem;
  color: hsl(var(--muted-foreground));
  font-weight: 500;
}

.batch-task-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.625rem;
  margin-bottom: 0.25rem;
}

.batch-size {
  color: hsl(var(--muted-foreground));
}

.batch-status {
  font-weight: 500;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.progress-bar {
  flex: 1;
  height: 0.25rem;
  background-color: hsl(var(--muted));
  border-radius: 0.125rem;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 0.125rem;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.625rem;
  font-weight: 500;
  color: hsl(var(--muted-foreground));
  min-width: 1.5rem;
  text-align: right;
}

.batch-task-actions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 0.125rem;
}

.expand-button {
  padding: 0.125rem;
  width: 1rem;
  height: 1rem;
}

/* 子任务样式 */
.sub-tasks-container {
  border-top: 1px solid hsl(var(--border));
  background-color: hsl(var(--muted) / 0.3);
}

.sub-tasks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.375rem 0.5rem;
  border-bottom: 1px solid hsl(var(--border));
  background-color: hsl(var(--muted) / 0.5);
}

.sub-tasks-title {
  font-weight: 600;
  font-size: 0.7rem;
  color: hsl(var(--foreground));
}

.sub-tasks-count {
  font-size: 0.625rem;
  color: hsl(var(--muted-foreground));
}

.sub-tasks-list {
  max-height: 120px;
  overflow-y: auto;
}

.sub-task-item {
  display: flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  gap: 0.375rem;
  border-bottom: 1px solid hsl(var(--border));
  transition: background-color 0.2s ease;
}

.sub-task-item:hover {
  background-color: hsl(var(--accent));
}

.sub-task-item:last-child {
  border-bottom: none;
}

.sub-task-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
}

.sub-task-info {
  flex: 1;
  min-width: 0;
}

.sub-task-name {
  font-size: 0.7rem;
  font-weight: 500;
  color: hsl(var(--foreground));
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 0.0625rem;
}

.sub-task-meta {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.625rem;
}

.file-size {
  color: hsl(var(--muted-foreground));
}

.task-status {
  font-weight: 500;
}

.sub-task-progress {
  display: flex;
  align-items: center;
  min-width: 0;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  min-width: 0;
}

.progress-percent {
  font-size: 0.625rem;
  font-weight: 500;
  color: hsl(var(--muted-foreground));
  min-width: 1.5rem;
  text-align: right;
}

.mini-progress-bar {
  width: 2rem;
  height: 0.125rem;
  background-color: hsl(var(--muted));
  border-radius: 0.0625rem;
  overflow: hidden;
}

.mini-progress-fill {
  height: 100%;
  background-color: hsl(var(--primary));
  border-radius: 0.0625rem;
  transition: width 0.3s ease;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background-color: currentColor;
  opacity: 0.2;
}

.status-indicator svg {
  color: white;
  opacity: 1;
}

/* 自定义滚动条 */
.sub-tasks-list::-webkit-scrollbar {
  width: 4px;
}

.sub-tasks-list::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 2px;
}

.sub-tasks-list::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 2px;
}

.sub-tasks-list::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}
</style>
