<template>
  <div
    class="relative overflow-hidden transition-all duration-200 border rounded-lg group border-border bg-card hover:bg-accent hover:shadow-md"
    :class="{
      'col-span-full': viewMode === 'list',
      'flex flex-col w-full max-w-[120px]': viewMode === 'grid',
      'w-full max-w-none': viewMode === 'list'
    }">

    <!-- 网格视图布局 -->
    <template v-if="viewMode === 'grid'">
      <!-- 文件夹图标区域 -->
      <div class="w-full h-[70px] flex items-center justify-center bg-muted overflow-hidden rounded-t-lg">
        <FolderIcon class="w-6 h-6 text-amber-500" />
      </div>

      <!-- 文件夹信息 -->
      <div class="flex flex-col flex-1 min-h-0 gap-1 p-2">
        <div class="text-xs font-medium leading-4 truncate text-foreground" :title="folder.name">
          {{ folder.name }}
        </div>
        <div class="flex flex-col gap-0.5 text-[10px]">
          <span class="text-muted-foreground">{{ folder.files.length }} 个文件</span>
          <span class="text-muted-foreground">{{ formatFileSize(folder.totalSize) }}</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="absolute transition-opacity duration-200 opacity-0 top-1 right-1 group-hover:opacity-100">
        <Button variant="ghost" size="sm" class="w-6 h-6 p-0" @click="handleRemove">
          <X class="w-3 h-3" />
        </Button>
      </div>
    </template>

    <!-- 列表视图布局 -->
    <template v-else>
      <div
        class="flex items-center p-3 sm:p-4 gap-4 min-h-[60px] transition-colors duration-200 cursor-pointer hover:bg-accent"
        @click="handleToggleExpand">
        <!-- 展开/折叠图标 -->
        <div class="flex-shrink-0">
          <component :is="folder.expanded ? ChevronDown : ChevronRight" class="w-4 h-4 text-muted-foreground" />
        </div>

        <!-- 文件夹图标 -->
        <div class="flex items-center justify-center flex-shrink-0 w-8 h-8 rounded-md sm:w-10 sm:h-10 bg-muted">
          <FolderIcon class="w-4 h-4 sm:w-5 sm:h-5 text-amber-500" />
        </div>

        <!-- 文件夹基本信息 -->
        <div class="flex flex-col flex-1 min-w-0 gap-2">
          <div class="flex items-center justify-between gap-4">
            <span class="text-sm font-medium truncate text-foreground" :title="folder.name">
              {{ folder.name }}
            </span>
            <span class="text-sm text-muted-foreground shrink-0">{{ formatFileSize(folder.totalSize) }}</span>
          </div>

          <div class="text-xs text-muted-foreground">
            {{ folder.files.length }} 个文件
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex items-center gap-1 shrink-0">
          <Button variant="ghost" size="sm" @click.stop="handleRemove">
            <X class="w-4 h-4" />
          </Button>
        </div>
      </div>

      <!-- 展开内容（仅列表模式） -->
      <Transition name="folder-expand">
        <div v-if="folder.expanded" class="p-2 border-t border-border bg-muted/30">
          <div class="grid grid-cols-1 gap-1">
            <FileItem v-for="file in folder.files" :key="file.id" :file="file" view-mode="list"
              @remove="handleFileRemove" class="border rounded-md bg-card border-border" />
          </div>
        </div>
      </Transition>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ChevronDown, ChevronRight, X, Folder as FolderIcon } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { formatFileSize } from '@/lib/upload-utils'
import FileItem from './FileItem.vue'
import type { UploadFile } from './composables/useFileUpload'

// 文件夹数据类型
export interface FolderGroup {
  path: string
  name: string
  files: UploadFile[]
  totalSize: number
  expanded: boolean
}

// Props
const props = withDefaults(defineProps<{
  folder: FolderGroup
  viewMode?: 'grid' | 'list'
}>(), {
  viewMode: 'grid'
})

// Emits
const emit = defineEmits<{
  remove: [folderPath: string]
  'toggle-expand': [folderPath: string]
  'file-remove': [fileId: string]
}>()

// 处理移除文件夹
const handleRemove = () => {
  emit('remove', props.folder.path)
}

// 处理展开/折叠
const handleToggleExpand = () => {
  emit('toggle-expand', props.folder.path)
}

// 处理文件移除
const handleFileRemove = (fileId: string) => {
  emit('file-remove', fileId)
}
</script>

<style scoped>
/* 文件夹展开动画 */
.folder-expand-enter-active,
.folder-expand-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.folder-expand-enter-from,
.folder-expand-leave-to {
  max-height: 0;
  opacity: 0;
}

.folder-expand-enter-to,
.folder-expand-leave-from {
  max-height: 500px;
  opacity: 1;
}
</style>