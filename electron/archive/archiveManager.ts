import { EventEmitter } from "events";
import * as path from "path";
import * as fs from "fs/promises";
import * as os from "os";
import { spawn } from "child_process";
import type { ArchiveTask, ArchiveConfig, ArchiveOptions, ArchiveResult, ArchiveStatus, SmartPackingAnalysis, BatchPackingOptions } from "./types";

// 导入7zip-bin来获取7z可执行文件路径
let sevenBin: any;
try {
  sevenBin = require("7zip-bin");
  console.log(`📦 7zip-bin加载成功，路径: ${sevenBin.path7za}`);
} catch (error) {
  console.error(`❌ 7zip-bin加载失败:`, error);
  throw new Error(`7zip-bin不可用: ${error instanceof Error ? error.message : String(error)}`);
}

export class ArchiveManager extends EventEmitter {
  private tasks: Map<string, ArchiveTask> = new Map();
  private config: ArchiveConfig;
  private activeTasks: Set<string> = new Set();

  constructor(config?: Partial<ArchiveConfig>) {
    super();
    this.config = {
      compressionLevel: 0, // 默认仅存储，不压缩
      format: "7z",
      tempDir: os.tmpdir(),
      maxConcurrent: 2,
      ...config,
    };
  }

  /**
   * 智能分析是否应该进行批量打包
   */
  async analyzeSmartPacking(filePaths: string[], options?: BatchPackingOptions): Promise<SmartPackingAnalysis> {
    const threshold = options?.threshold || 50;
    const maxSize = options?.maxSize || 500 * 1024 * 1024; // 500MB

    try {
      let totalSize = 0;
      let fileCount = 0;

      // 计算总大小和文件数量
      for (const filePath of filePaths) {
        try {
          const stats = await fs.stat(filePath);
          if (stats.isFile()) {
            totalSize += stats.size;
            fileCount++;
          } else if (stats.isDirectory()) {
            const dirStats = await this.calculateDirectoryStats(filePath);
            totalSize += dirStats.size;
            fileCount += dirStats.fileCount;
          }
        } catch (error) {
          console.warn(`无法获取文件统计信息: ${filePath}`, error);
        }
      }

      const shouldPack = fileCount >= threshold && totalSize <= maxSize;

      // 估算打包时间（基于文件数量和大小）
      const estimatedPackTime = Math.max(5, Math.ceil(fileCount / 100) + Math.ceil(totalSize / (50 * 1024 * 1024)));

      // 估算节省的上传时间（假设打包后减少HTTP请求开销）
      const estimatedUploadTimeSavings = Math.max(0, (fileCount - 1) * 2); // 每个文件节省2秒的HTTP开销

      return {
        shouldPack,
        reason: shouldPack
          ? `文件数量 ${fileCount} 超过阈值 ${threshold}，建议打包上传`
          : fileCount < threshold
          ? `文件数量 ${fileCount} 未达到阈值 ${threshold}`
          : `文件总大小 ${this.formatFileSize(totalSize)} 超过限制 ${this.formatFileSize(maxSize)}`,
        fileCount,
        totalSize,
        estimatedPackTime,
        estimatedSavings: {
          uploadTime: estimatedUploadTimeSavings,
          bandwidth: Math.max(0, totalSize * 0.1), // 假设能节省10%的带宽
        },
      };
    } catch (error) {
      return {
        shouldPack: false,
        reason: `分析失败: ${error}`,
        fileCount: 0,
        totalSize: 0,
        estimatedPackTime: 0,
        estimatedSavings: { uploadTime: 0, bandwidth: 0 },
      };
    }
  }

  /**
   * 创建压缩任务
   */
  async createArchiveTask(sourcePaths: string[], options?: ArchiveOptions): Promise<string> {
    const taskId = this.generateTaskId();
    const archiveName = options?.name || `archive_${Date.now()}`;
    const outputPath = path.join(this.config.tempDir, `${archiveName}.${this.config.format}`);

    // 计算总文件数
    let totalFiles = 0;
    for (const sourcePath of sourcePaths) {
      try {
        const stats = await fs.stat(sourcePath);
        if (stats.isFile()) {
          totalFiles++;
        } else if (stats.isDirectory()) {
          const dirStats = await this.calculateDirectoryStats(sourcePath);
          totalFiles += dirStats.fileCount;
        }
      } catch (error) {
        console.warn(`无法统计文件: ${sourcePath}`, error);
      }
    }

    const task: ArchiveTask = {
      id: taskId,
      name: archiveName,
      sourcePaths,
      outputPath,
      status: "pending",
      progress: 0,
      totalFiles,
      processedFiles: 0,
      startTime: new Date(),
      metadata: options?.metadata,
    };

    this.tasks.set(taskId, task);
    this.emit("task-created", taskId, task);

    return taskId;
  }

  /**
   * 开始压缩任务
   */
  async startArchiveTask(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`压缩任务不存在: ${taskId}`);
    }

    if (this.activeTasks.size >= this.config.maxConcurrent) {
      throw new Error("已达到最大并发压缩任务数");
    }

    this.activeTasks.add(taskId);
    this.updateTaskStatus(taskId, "compressing");

    try {
      const result = await this.performCompression(task);
      this.updateTaskStatus(taskId, "completed");
      this.emit("task-completed", taskId, result);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.updateTaskStatus(taskId, "error", errorMessage);
      this.emit("task-error", taskId, errorMessage);
      throw error;
    } finally {
      this.activeTasks.delete(taskId);
    }
  }

  /**
   * 取消压缩任务
   */
  async cancelArchiveTask(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`压缩任务不存在: ${taskId}`);
    }

    this.updateTaskStatus(taskId, "cancelled");
    this.activeTasks.delete(taskId);
    this.emit("task-cancelled", taskId);

    // 清理临时文件
    try {
      await fs.unlink(task.outputPath);
    } catch (error) {
      console.warn(`清理临时文件失败: ${task.outputPath}`, error);
    }
  }

  /**
   * 获取任务信息
   */
  getTask(taskId: string): ArchiveTask | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * 获取所有任务
   */
  getAllTasks(): ArchiveTask[] {
    return Array.from(this.tasks.values());
  }

  /**
   * 清理已完成的任务
   */
  async cleanupCompletedTasks(): Promise<void> {
    const completedTasks = Array.from(this.tasks.values()).filter((task) => ["completed", "error", "cancelled"].includes(task.status));

    for (const task of completedTasks) {
      try {
        // 清理临时文件
        await fs.unlink(task.outputPath);
      } catch (error) {
        console.warn(`清理临时文件失败: ${task.outputPath}`, error);
      }
      this.tasks.delete(task.id);
    }
  }

  // ========== 私有方法 ==========

  private generateTaskId(): string {
    return `archive_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private async calculateDirectoryStats(dirPath: string): Promise<{ size: number; fileCount: number }> {
    let size = 0;
    let fileCount = 0;

    try {
      // 首先检查路径是否确实是目录
      const pathStats = await fs.stat(dirPath);
      if (!pathStats.isDirectory()) {
        console.warn(`路径不是目录，跳过统计: ${dirPath}`);
        return { size: 0, fileCount: 0 };
      }

      const entries = await fs.readdir(dirPath, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);

        if (entry.isFile()) {
          const stats = await fs.stat(fullPath);
          size += stats.size;
          fileCount++;
        } else if (entry.isDirectory()) {
          const subStats = await this.calculateDirectoryStats(fullPath);
          size += subStats.size;
          fileCount += subStats.fileCount;
        }
      }
    } catch (error) {
      console.warn(`计算目录统计信息失败: ${dirPath}`, error);
    }

    return { size, fileCount };
  }

  /**
   * 计算源路径的公共父目录和相对路径
   */
  private calculateRelativePaths(sourcePaths: string[]): { workingDir: string; relativePaths: string[] } {
    if (sourcePaths.length === 0) {
      throw new Error("源路径列表为空");
    }

    if (sourcePaths.length === 1) {
      // 单个路径的情况
      const singlePath = sourcePaths[0];
      const parentDir = path.dirname(singlePath);
      const relativePath = path.basename(singlePath);
      return {
        workingDir: parentDir,
        relativePaths: [relativePath],
      };
    }

    // 多个路径的情况，找到公共父目录
    const normalizedPaths = sourcePaths.map((p) => path.resolve(p));

    // 找到最短路径作为基准
    const shortestPath = normalizedPaths.reduce((shortest, current) => (current.length < shortest.length ? current : shortest));

    // 从最短路径开始，向上查找公共父目录
    let commonParent = path.dirname(shortestPath);

    while (commonParent !== path.dirname(commonParent)) {
      // 直到根目录
      const isCommonParent = normalizedPaths.every((p) => p.startsWith(commonParent + path.sep) || p === commonParent);
      if (isCommonParent) {
        break;
      }
      commonParent = path.dirname(commonParent);
    }

    // 计算相对路径
    const relativePaths = normalizedPaths.map((p) => path.relative(commonParent, p));

    console.log(`📦 计算相对路径: 公共父目录=${commonParent}`);
    console.log(
      `📦 相对路径映射:`,
      normalizedPaths.map((abs, i) => `${abs} -> ${relativePaths[i]}`)
    );

    return {
      workingDir: commonParent,
      relativePaths,
    };
  }

  /**
   * 调试：列出将要压缩的文件
   */
  private async debugListFiles(workingDir: string, relativePaths: string[]): Promise<void> {
    console.log(`📦 调试：列出将要压缩的文件`);

    for (const relativePath of relativePaths) {
      const fullPath = path.join(workingDir, relativePath);
      try {
        const stats = await fs.stat(fullPath);
        if (stats.isDirectory()) {
          console.log(`📁 目录: ${relativePath}`);
          await this.debugListDirectoryContents(fullPath, relativePath, 0);
        } else {
          console.log(`📄 文件: ${relativePath} (${this.formatFileSize(stats.size)})`);
        }
      } catch (error) {
        console.warn(`⚠️ 无法访问: ${relativePath}`, error);
      }
    }
  }

  /**
   * 递归列出目录内容（用于调试）
   */
  private async debugListDirectoryContents(fullPath: string, relativePath: string, depth: number): Promise<void> {
    if (depth > 3) {
      // 限制递归深度
      console.log(`${"  ".repeat(depth + 1)}... (深度限制)`);
      return;
    }

    try {
      const entries = await fs.readdir(fullPath, { withFileTypes: true });
      for (const entry of entries) {
        const entryRelativePath = path.join(relativePath, entry.name);
        const entryFullPath = path.join(fullPath, entry.name);

        if (entry.isFile()) {
          const stats = await fs.stat(entryFullPath);
          console.log(`${"  ".repeat(depth + 1)}📄 ${entry.name} (${this.formatFileSize(stats.size)})`);
        } else if (entry.isDirectory()) {
          console.log(`${"  ".repeat(depth + 1)}📁 ${entry.name}/`);
          await this.debugListDirectoryContents(entryFullPath, entryRelativePath, depth + 1);
        }
      }
    } catch (error) {
      console.warn(`⚠️ 无法读取目录: ${relativePath}`, error);
    }
  }

  private async performCompression(task: ArchiveTask): Promise<ArchiveResult> {
    const startTime = Date.now();

    try {
      // 确保输出目录存在
      await fs.mkdir(path.dirname(task.outputPath), { recursive: true });

      // 计算公共父目录和相对路径
      const { workingDir, relativePaths } = this.calculateRelativePaths(task.sourcePaths);

      console.log(`📦 工作目录: ${workingDir}`);
      console.log(`📦 相对路径:`, relativePaths);

      // 调试：列出将要压缩的文件
      await this.debugListFiles(workingDir, relativePaths);

      // 构建7z命令参数，使用排除功能过滤系统文件和隐藏文件
      const args = [
        "a", // 添加到压缩包
        "-t7z", // 7z格式
        `-mx${this.config.compressionLevel}`, // 压缩级别
        "-y", // 自动回答yes
        "-bb1", // 设置输出日志级别

        // macOS 系统文件
        "-xr!.DS_Store", // 排除所有.DS_Store文件
        "-xr!.AppleDouble", // 排除AppleDouble文件
        "-xr!.LSOverride", // 排除Launch Services覆盖文件
        "-xr!.Trashes", // 排除macOS垃圾箱文件夹
        "-xr!.Spotlight-V100", // 排除macOS Spotlight索引
        "-xr!.fseventsd", // 排除macOS文件系统事件
        "-xr!.DocumentRevisions-V100", // 排除文档版本控制
        "-xr!.TemporaryItems", // 排除临时项目
        "-xr!.VolumeIcon.icns", // 排除卷图标
        "-xr!.com.apple.timemachine.donotpresent", // 排除Time Machine标记

        // Windows 系统文件
        "-xr!Thumbs.db", // 排除所有Thumbs.db文件
        "-xr!desktop.ini", // 排除所有desktop.ini文件
        "-xr!ehthumbs.db", // 排除缩略图缓存
        "-xr!$RECYCLE.BIN", // 排除回收站
        "-xr!System Volume Information", // 排除系统卷信息

        // 版本控制和开发工具目录
        "-xr!.git", // 排除git目录
        "-xr!.svn", // 排除svn目录
        "-xr!.hg", // 排除mercurial目录
        "-xr!.bzr", // 排除bazaar目录
        "-xr!CVS", // 排除CVS目录
        "-xr!node_modules", // 排除node_modules目录
        "-xr!.npm", // 排除npm缓存
        "-xr!.yarn", // 排除yarn缓存
        "-xr!bower_components", // 排除bower组件

        // IDE和编辑器文件
        "-xr!.vscode", // 排除VSCode配置
        "-xr!.idea", // 排除IntelliJ IDEA配置
        "-xr!*.swp", // 排除vim交换文件
        "-xr!*.swo", // 排除vim交换文件
        "-xr!*~", // 排除vim备份文件
        "-xr!.#*", // 排除emacs锁文件

        // 临时文件和缓存
        "-xr!~*", // 排除临时文件
        "-xr!*.tmp", // 排除临时文件
        "-xr!*.temp", // 排除临时文件
        "-xr!*.bak", // 排除备份文件
        "-xr!*.cache", // 排除缓存文件
        "-xr!*.log", // 排除日志文件

        task.outputPath,
        ...relativePaths,
      ];

      console.log(`📦 7z命令参数:`, args);
      console.log(`📦 输出路径: ${task.outputPath}`);
      console.log(`📦 源路径数量: ${task.sourcePaths.length}`);

      // 执行7z压缩，在工作目录下执行
      await this.execute7zCommand(args, task, workingDir);

      // 获取压缩结果统计
      const stats = await this.getCompressionStats(task);
      const duration = Date.now() - startTime;

      return {
        success: true,
        archivePath: task.outputPath,
        stats: {
          ...stats,
          duration,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  private async execute7zCommand(args: string[], task: ArchiveTask, workingDir?: string): Promise<void> {
    return new Promise((resolve, reject) => {
      console.log(`📦 开始执行7z命令: ${sevenBin.path7za} ${args.join(" ")}`);
      console.log(`📦 7zip-bin路径: ${sevenBin.path7za}`);
      if (workingDir) {
        console.log(`📦 工作目录: ${workingDir}`);
      }

      const process = spawn(sevenBin.path7za, args, {
        cwd: workingDir, // 设置工作目录
      });
      let stderr = "";
      let stdout = "";

      process.stderr.on("data", (data) => {
        const errorText = data.toString();
        stderr += errorText;
        console.log(`📦 7z stderr: ${errorText}`);
      });

      process.stdout.on("data", (data) => {
        // 解析7z输出来更新进度
        const output = data.toString();
        stdout += output;
        console.log(`📦 7z stdout: ${output}`);
        this.parseProgressFromOutput(output, task);
      });

      process.on("close", (code) => {
        console.log(`📦 7z进程结束，退出码: ${code}`);
        console.log(`📦 完整stdout: ${stdout}`);
        console.log(`📦 完整stderr: ${stderr}`);

        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`7z压缩失败，退出码: ${code}, 错误: ${stderr}`));
        }
      });

      process.on("error", (error) => {
        console.error(`📦 7z进程启动失败:`, error);
        reject(new Error(`7z进程启动失败: ${error.message}`));
      });
    });
  }

  private parseProgressFromOutput(output: string, task: ArchiveTask): void {
    // 简单的进度解析，可以根据7z的实际输出格式进行优化
    const lines = output.split("\n");
    for (const line of lines) {
      if (line.includes("Compressing")) {
        const match = line.match(/Compressing\s+(.+)/);
        if (match) {
          task.currentFile = match[1];
          task.processedFiles++;
          task.progress = Math.min(95, Math.round((task.processedFiles / task.totalFiles) * 100));
          this.emit("task-progress", task.id, task.progress, task.currentFile);
        }
      }
    }
  }

  private async getCompressionStats(task: ArchiveTask): Promise<{
    originalSize: number;
    compressedSize: number;
    compressionRatio: number;
    fileCount: number;
  }> {
    try {
      // 计算原始大小
      let originalSize = 0;
      let fileCount = 0;

      for (const sourcePath of task.sourcePaths) {
        try {
          const pathStats = await fs.stat(sourcePath);
          if (pathStats.isFile()) {
            originalSize += pathStats.size;
            fileCount++;
          } else if (pathStats.isDirectory()) {
            const stats = await this.calculateDirectoryStats(sourcePath);
            originalSize += stats.size;
            fileCount += stats.fileCount;
          }
        } catch (error) {
          console.warn(`无法获取源路径统计信息: ${sourcePath}`, error);
        }
      }

      // 获取压缩后大小
      const compressedStats = await fs.stat(task.outputPath);
      const compressedSize = compressedStats.size;
      const compressionRatio = originalSize > 0 ? (1 - compressedSize / originalSize) * 100 : 0;

      return {
        originalSize,
        compressedSize,
        compressionRatio,
        fileCount,
      };
    } catch (error) {
      console.warn("获取压缩统计信息失败:", error);
      return {
        originalSize: 0,
        compressedSize: 0,
        compressionRatio: 0,
        fileCount: 0,
      };
    }
  }

  private updateTaskStatus(taskId: string, status: ArchiveStatus, error?: string): void {
    const task = this.tasks.get(taskId);
    if (!task) return;

    task.status = status;
    if (error) {
      task.error = error;
    }
    if (status === "completed" || status === "error" || status === "cancelled") {
      task.endTime = new Date();
    }
  }

  private formatFileSize(bytes: number): string {
    const units = ["B", "KB", "MB", "GB"];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }
}
