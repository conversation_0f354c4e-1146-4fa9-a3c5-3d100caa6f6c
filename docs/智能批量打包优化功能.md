# 智能批量打包优化功能

## 功能概述

智能批量打包优化功能是一个自动化的文件上传优化系统，当检测到大量文件（超过50个）的文件夹上传时，会自动建议将文件打包成7z压缩包后再上传，以提高上传效率和减少网络请求数量。

## 主要特性

### 🔍 智能检测
- **触发条件**：文件夹上传任务中的文件数量超过50个时自动触发
- **分析算法**：综合考虑文件数量、总大小、预计打包时间等因素
- **智能建议**：根据分析结果提供最优的上传策略建议

### 📦 高效打包
- **压缩格式**：使用7z格式，兼容性好，压缩效率高
- **压缩级别**：设为0（仅存储，不压缩）以获得最快打包速度
- **结构保持**：完整保持原有的文件夹层级结构
- **命名规则**：使用文件夹名称作为压缩包名称

### 🚀 性能优化
- **并发控制**：最大2个并发压缩任务，避免系统负载过高
- **内存优化**：流式处理，避免大文件占用过多内存
- **临时存储**：使用系统临时目录，上传完成后自动清理
- **错误恢复**：打包失败时自动回退到逐个文件上传模式

### 👤 用户体验
- **确认对话框**：打包前显示详细信息和预计收益
- **进度显示**：实时显示打包进度和当前处理的文件
- **状态反馈**：清晰的状态提示和错误信息
- **灵活选择**：用户可选择打包上传、逐个上传或取消操作

## 技术架构

### 模块结构
```
electron/
├── archive/                    # 压缩模块
│   ├── archiveManager.ts      # 核心压缩管理器
│   ├── ipcHandlers.ts         # IPC处理器
│   ├── preloadApi.ts          # Preload API
│   ├── types.ts               # 类型定义
│   └── index.ts               # 模块入口
├── tus/                       # 上传模块（集成智能打包）
│   ├── uploadManager.ts       # 上传管理器（新增智能打包方法）
│   ├── ipcHandlers.ts         # IPC处理器（新增智能打包API）
│   └── preloadApi.ts          # Preload API（新增智能打包接口）
└── main.ts                    # 主进程（初始化压缩模块）

src/components/Upload/composables/
└── useTusUpload.ts            # Vue组合式API（集成智能打包逻辑）
```

### 核心流程
1. **检测阶段**：分析上传文件，判断是否需要智能打包
2. **分析阶段**：计算文件数量、大小、预计收益等指标
3. **确认阶段**：向用户展示分析结果，获取用户选择
4. **打包阶段**：使用7z进行文件打包，显示进度
5. **上传阶段**：将压缩包作为单个文件上传
6. **清理阶段**：上传完成后清理临时文件

## API接口

### 主要方法

#### `analyzeSmartPacking(filePaths, options)`
分析是否应该进行智能打包
- **参数**：
  - `filePaths`: 文件路径数组
  - `options`: 分析选项（阈值、最大大小等）
- **返回**：分析结果对象

#### `smartPackUpload(filePaths, options)`
执行智能打包上传
- **参数**：
  - `filePaths`: 文件路径数组
  - `options`: 上传选项（压缩包名称、元数据等）
- **返回**：上传任务信息

### 事件系统
- `archive-task-created`: 压缩任务创建
- `archive-task-progress`: 压缩进度更新
- `archive-task-completed`: 压缩任务完成
- `archive-task-error`: 压缩任务错误

## 配置选项

### 默认配置
```typescript
{
  threshold: 50,                    // 文件数量阈值
  maxSize: 500 * 1024 * 1024,      // 最大文件大小限制（500MB）
  compressionLevel: 0,              // 压缩级别（0=仅存储）
  format: "7z",                     // 压缩格式
  maxConcurrent: 2,                 // 最大并发数
  autoCleanup: true                 // 自动清理临时文件
}
```

### 自定义配置
可以通过环境变量或配置文件自定义相关参数。

## 使用示例

### 前端调用
```typescript
import { useTusUpload } from '@/components/Upload/composables/useTusUpload'

const { uploadFiles } = useTusUpload()

// 上传文件（自动检测是否需要智能打包）
await uploadFiles(files, metadata, callbacks)
```

### 手动调用智能打包
```typescript
const api = window.electronAPI

// 分析是否需要打包
const analysis = await api.tus.analyzeSmartPacking(filePaths, {
  threshold: 50,
  maxSize: 500 * 1024 * 1024
})

if (analysis.success && analysis.data.shouldPack) {
  // 执行智能打包上传
  const result = await api.tus.smartPackUpload(filePaths, {
    archiveName: 'my_folder',
    metadata: { uploadType: 'smart-packed' }
  })
}
```

## 错误处理

### 常见错误及解决方案
1. **7z可执行文件不存在**：自动下载或提示用户安装
2. **磁盘空间不足**：检查临时目录空间，提示用户清理
3. **文件权限问题**：提示用户检查文件权限
4. **压缩失败**：自动回退到逐个文件上传模式
5. **网络中断**：支持断点续传和重试机制

### 日志记录
所有操作都有详细的日志记录，便于问题排查和性能分析。

## 性能指标

### 预期收益
- **上传时间**：大量小文件场景下可节省20-50%的上传时间
- **网络请求**：将N个文件的请求合并为1个请求
- **带宽利用**：减少HTTP协议开销，提高带宽利用率
- **服务器负载**：减少服务器处理的请求数量

### 适用场景
- ✅ 大量小文件（如代码项目、图片集合）
- ✅ 文件夹结构复杂的项目
- ✅ 网络延迟较高的环境
- ❌ 单个大文件（无需打包）
- ❌ 已经是压缩格式的文件

## 未来规划

### 计划功能
1. **智能压缩级别**：根据文件类型自动选择最优压缩级别
2. **增量打包**：支持文件夹的增量更新打包
3. **多格式支持**：支持zip、tar等多种压缩格式
4. **云端解压**：服务端自动解压并恢复文件结构
5. **统计分析**：提供详细的打包和上传统计报告

### 性能优化
1. **多线程压缩**：利用多核CPU提高压缩速度
2. **内存优化**：进一步减少内存占用
3. **缓存机制**：缓存常用文件的压缩结果
4. **预测算法**：基于历史数据优化打包策略

---

*该功能已集成到现有的文件上传系统中，用户无需额外配置即可享受智能打包优化带来的性能提升。*
